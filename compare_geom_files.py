#!/usr/bin/env python3
"""
几何文件对比脚本
比较CSV和TXT文件中的face_id和geom数据，按面积差异分类输出结果
"""

import argparse
import csv
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import sys

from shapely import wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('geom_compare.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def load_geom_data(file_path: Path, delimiter: str = None) -> Dict[str, Tuple[str, float]]:
    """
    加载几何数据文件
    
    Args:
        file_path: 文件路径
        delimiter: 分隔符
        
    Returns:
        Dict[face_id, (geom_wkt, area)]
    """
    logger = logging.getLogger(__name__)
    data = {}

    # 设置CSV字段大小限制
    csv.field_size_limit(sys.maxsize)

    # 调整分隔符检测逻辑 - CSV文件也使用制表符
    if delimiter is None:
        delimiter = '\t'  # 统一使用制表符，因为CSV也是通过COPY命令生成的
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f, delimiter=delimiter)
            
            # 清理列名中的空格
            fieldnames = [name.strip() for name in reader.fieldnames]
            logger.info(f"检测到的列名: {fieldnames}")
            
            # 检查必需的列
            if 'face_id' not in fieldnames or 'geom' not in fieldnames:
                raise ValueError(f"文件 {file_path} 缺少必需的列: face_id, geom。实际列名: {fieldnames}")
            
            for row_num, row in enumerate(tqdm(reader, desc=f"加载 {file_path.name}")):
                # 处理列名可能有空格的情况
                face_id = None
                geom_wkt = None
                
                for key, value in row.items():
                    clean_key = key.strip()
                    if clean_key == 'face_id':
                        face_id = value.strip() if value else ''
                    elif clean_key == 'geom':
                        geom_wkt = value.strip() if value else ''
                
                if not face_id or not geom_wkt:
                    logger.warning(f"{file_path.name} 第{row_num+2}行: face_id或geom为空")
                    continue
                
                try:
                    # 去除SRID前缀，只保留纯WKT部分
                    if geom_wkt.startswith('SRID='):
                        # 找到分号位置，去除SRID部分
                        semicolon_pos = geom_wkt.find(';')
                        if semicolon_pos != -1:
                            geom_wkt = geom_wkt[semicolon_pos + 1:]
                    
                    # 解析几何并计算面积
                    geom = wkt.loads(geom_wkt)
                    area = geom.area
                    data[face_id] = (geom_wkt, area)
                    
                except Exception as e:
                    logger.warning(f"{file_path.name} 第{row_num+2}行: 几何解析失败 - {e}")
                    continue
                    
    except Exception as e:
        logger.error(f"读取文件 {file_path} 失败: {e}")
        raise
    
    logger.info(f"成功加载 {file_path.name}: {len(data)} 条记录")
    return data


def compare_geom_data(csv_data: Dict, txt_data: Dict, area_threshold: float = 100.0) -> Tuple[List, List, List]:
    """
    比较两个几何数据集
    
    Args:
        csv_data: CSV文件数据
        txt_data: TXT文件数据  
        area_threshold: 面积差异阈值（平方米）
        
    Returns:
        (匹配且面积相近, 匹配但面积差异过大, 仅在TXT存在)
    """
    logger = logging.getLogger(__name__)
    
    matched_similar = []      # 匹配且面积相近
    matched_different = []    # 匹配但面积差异过大
    txt_only = []            # 仅在TXT存在
    
    # 获取所有face_id
    csv_face_ids = set(csv_data.keys())
    txt_face_ids = set(txt_data.keys())
    
    # 处理匹配的face_id
    common_face_ids = csv_face_ids & txt_face_ids
    logger.info(f"共同face_id数量: {len(common_face_ids)}")
    
    for face_id in tqdm(common_face_ids, desc="比较匹配记录"):
        csv_geom_wkt, csv_area = csv_data[face_id]
        txt_geom_wkt, txt_area = txt_data[face_id]
        
        area_diff = abs(csv_area - txt_area)
        
        record = {
            'face_id': face_id,
            'csv_geom': csv_geom_wkt,
            'csv_area': csv_area,
            'txt_geom': txt_geom_wkt, 
            'txt_area': txt_area,
            'area_diff': area_diff
        }
        
        if area_diff <= area_threshold:
            matched_similar.append(record)
        else:
            matched_different.append(record)
    
    # 处理仅在TXT存在的face_id
    txt_only_face_ids = txt_face_ids - csv_face_ids
    logger.info(f"仅在TXT存在的face_id数量: {len(txt_only_face_ids)}")
    
    for face_id in tqdm(txt_only_face_ids, desc="处理TXT独有记录"):
        txt_geom_wkt, txt_area = txt_data[face_id]
        txt_only.append({
            'face_id': face_id,
            'geom': txt_geom_wkt,
            'area': txt_area
        })
    
    logger.info(f"匹配且面积相近: {len(matched_similar)} 条")
    logger.info(f"匹配但面积差异过大: {len(matched_different)} 条")
    logger.info(f"仅在TXT存在: {len(txt_only)} 条")
    
    return matched_similar, matched_different, txt_only


def save_results(output_dir: Path, matched_similar: List, matched_different: List, txt_only: List):
    """保存比较结果"""
    logger = logging.getLogger(__name__)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存匹配且面积相近的记录
    similar_path = output_dir / "matched_similar_area.csv"
    if matched_similar:
        with open(similar_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['face_id', 'csv_geom', 'csv_area', 'txt_geom', 'txt_area', 'area_diff'])
            writer.writeheader()
            writer.writerows(matched_similar)
        logger.info(f"保存匹配且面积相近记录: {similar_path}")
    
    # 保存匹配但面积差异过大的记录
    different_path = output_dir / "matched_different_area.csv"
    if matched_different:
        with open(different_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['face_id', 'csv_geom', 'csv_area', 'txt_geom', 'txt_area', 'area_diff'])
            writer.writeheader()
            writer.writerows(matched_different)
        logger.info(f"保存匹配但面积差异过大记录: {different_path}")
    
    # 保存仅在TXT存在的记录
    txt_only_path = output_dir / "txt_only.csv"
    if txt_only:
        with open(txt_only_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['face_id', 'geom', 'area'])
            writer.writeheader()
            writer.writerows(txt_only)
        logger.info(f"保存仅在TXT存在记录: {txt_only_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="比较CSV和TXT文件中的几何数据")
    parser.add_argument("csv_file", type=Path, help="CSV输入文件路径")
    parser.add_argument("txt_file", type=Path, help="TXT输入文件路径")
    parser.add_argument("-o", "--output", type=Path, default="output", help="输出目录路径")
    parser.add_argument("-t", "--threshold", type=float, default=100.0, help="面积差异阈值（平方米）")
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    try:
        # 验证输入文件
        if not args.csv_file.exists():
            raise FileNotFoundError(f"CSV文件不存在: {args.csv_file}")
        if not args.txt_file.exists():
            raise FileNotFoundError(f"TXT文件不存在: {args.txt_file}")
        
        logger.info(f"开始处理文件:")
        logger.info(f"  CSV文件: {args.csv_file}")
        logger.info(f"  TXT文件: {args.txt_file}")
        logger.info(f"  输出目录: {args.output}")
        logger.info(f"  面积阈值: {args.threshold} 平方米")
        
        # 加载数据
        csv_data = load_geom_data(args.csv_file)
        txt_data = load_geom_data(args.txt_file)
        
        # 比较数据
        matched_similar, matched_different, txt_only = compare_geom_data(
            csv_data, txt_data, args.threshold
        )
        
        # 保存结果
        save_results(args.output, matched_similar, matched_different, txt_only)
        
        logger.info("处理完成!")
        
    except Exception as e:
        logger.error(f"处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
