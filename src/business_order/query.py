""" 商单查询函数 """
import itertools
import os
import sys
import uuid
import shapely.wkt
import shapely.ops
from tqdm import tqdm
from src.tools import pgsql, tsv
from src.business_order import helper, enums
from psycopg2 import sql as pg_sql_builer


class OrderQuery:
    """商单助手函数"""

    @staticmethod
    def get_sdconf(export_id):
        """获取日志，主要用于查询成果库配置"""
        with helper.create_beeflow_connection() as conn, conn.cursor() as curs:
            q_sql = f"""
                select id, export_id, remark from business_order_export_step_log 
                where export_id = %s and step_name = %s
                and status_code = 0 and remark <> '' order by id desc
            """
            curs.execute(q_sql, (export_id, enums.StepName.ReadyInitDeliveryDB))
            row = curs.fetchone()

            return row

    @staticmethod
    def get_export_order_list(status="", export_id=0, database="BUSINESS_ORDER"):
        """获取待处理商单列表"""
        if status == "":
            status = enums.CommonExportStatus.INIT
        with helper.create_beeflow_connection() as conn, conn.cursor() as curs:
            where_params = [status, database]
            sql = f"""
                select id, status, req, remark, result, conditions from common_export 
                where status = %s and query_database=%s
            """
            if export_id > 0:
                sql += " and id=%s"
                where_params.append(export_id)
            curs.execute(sql, where_params)
            return curs.fetchall()

    @staticmethod
    def get_division_geom_from_pddlastest(conn, table, code_type, codes):
        """获取geom"""

        sql = f"""
            select st_astext(st_union(geom)) as geom from sd_bad_range where division_type = %s
            and code=ANY(%s);
        """

        row = pgsql.fetch_one(conn, sql, [code_type, codes])
        if row and row[0]:
            return row[0]

        return 'POLYGON EMPTY'

    @staticmethod
    def get_division_geom(conn, table, code_type, codes):
        """获取geom"""

        sql = f"""
            select St_AsText(ST_UnaryUnion(ST_Collect(geom))) as geom from
            {table} where {code_type}_code = any(%s);
        """
        row = pgsql.fetch_one(conn, sql, [codes])
        if row and row[0]:
            return row[0]

        return 'POLYGON EMPTY'

    @staticmethod
    def get_bad_name(conn, table, code_type, codes) -> list:
        """获取行政区划名称、图幅用"""

        sql = f"""
            select distinct {code_type}_code as code,{code_type}_name as name 
            from {table} where {code_type}_code = any(%s)
        """
        bads = pgsql.fetch_all(conn, sql, [codes])

        res = []
        if code_type == 'city':
            for bad in bads:
                if bad[0] in ['310100000000']:
                    res.append('上海市')
                    continue
                if bad[0] in ['120100000000']:
                    res.append('天津市')
                    continue
                if bad[0] in ['500100000000']:
                    res.append('重庆市')
                    continue
                if bad[0] in ['110100000000']:
                    res.append('北京市')
                    continue
                res.append(bad[1])
        else:
            res = [x[1] for x in bads]

        if '北京市' in res:
            res.append('北京')
        if '天津' in res:
            res.append('天津')
        return res

    @staticmethod
    def get_mesh_ids(conn, code_type, names) -> list:
        """获取图幅ids"""
        sql = 'select mesh_id from mesh_conf where 1=1'
        if code_type == 'pro':
            sql += ' and province_ch=any(%s)'
        if code_type == 'city':
            sql += ' and cityname=any(%s)'
        rows = pgsql.fetch_all(conn, sql, [names])
        return [x[0] for x in rows]

    @staticmethod
    def fetch_in_batches(cursor, query, params, batch_size=10000):
        """数据查询迭代器"""
        cursor.execute(query, params)
        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break
            yield rows

    @staticmethod
    def fetch_in_cursor(cursor, sql, where_params, batch_size=2000):
        """ 游标读取数据 """
        cursor_name = f"sd_cursor_{uuid.uuid4().hex}"
        cursor.execute(
            f"DECLARE {cursor_name} CURSOR FOR {sql};", where_params)
        try:
            while True:
                # 每次批量读取 batch_size 条数据
                cursor.execute(f"FETCH FORWARD {batch_size} FROM {cursor_name};")
                rows = cursor.fetchall()
                if not rows:  # 数据读取完毕
                    break

                yield rows  # 返回当前批次数据，供调用方处理
        finally:
            # 关闭游标
            cursor.execute(f"CLOSE {cursor_name};")

    @staticmethod
    def generate_segments(cursor, tab, pk_key, where='', size=20000):
        """生成查询组"""
        sql = f"""
        WITH numbered AS (
            SELECT {pk_key}, CEIL(ROW_NUMBER() OVER (ORDER BY {pk_key}) / {size}) AS batch
            FROM {tab} {where}
        )
        SELECT MIN({pk_key}) AS start_id, MAX({pk_key}) AS end_id FROM numbered
        GROUP BY batch ORDER BY start_id;
        """
        cursor.execute(sql)
        return cursor.fetchall()

    @staticmethod
    def get_count(conn, tab, where, where_params=None):
        """ 数量查询 """
        sql = f"select count(*) as co from {tab} where 1=1 {where};"

        row = pgsql.fetch_one(conn, sql, where_params)
        if row:
            return row[0]

        return 0

    @staticmethod
    def copy_table_to_file(conf, outpath, sql, where_params):
        """ 从数据库导出数据到文件 """

        with pgsql.get_connection(conf) as conn, conn.cursor() as curs:
            with open(outpath, "w") as f:
                final_query = curs.mogrify(sql, where_params).decode("utf-8")  # 安全拼接 SQL
                # print(final_query)
                # print(where_params)
                curs.copy_expert(f"COPY ({final_query}) TO STDOUT WITH CSV DELIMITER E'\t' HEADER", f)

    @staticmethod
    def copy_quanguo_aoi_from_master(conf, export_id):
        """ 复制主表数据到csv """
        sql = f"""
            select face_id,kind,mesh_id,st_astext(geom) as geom from blu_face 
            where 1=1 order by face_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"quanguo_aoi.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, [])

        return file_path

    @staticmethod
    def copy_quanguo_water_from_master(conf, export_id):
        """ 复制主表数据到csv """
        sql = f"""
            select face_id,name_ch,kind,mesh_id,st_astext(geom) as geom from blc_face 
            where kind in ('1', '2', '3', '4', '5', '6', '31', '32', '33') 
            order by face_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"quanguo_water.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, [])

        return file_path

    @staticmethod
    def copy_bud_from_master(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select a.face_id as face_id, a.struct_id as struct_id,
            b.name_ch as name_ch, a.aoi_id as aoi_id,a.height as height,
            ST_Area(a.geom::geography) as area, 
            st_perimeter(a.geom::geography) as perimeter,
            a.mesh_id as mesh_id, a.src as src, a.create_time as create_time,
            st_astext(a.geom) as geom
            from bud_face a left join bud_struct b on a.struct_id = b.struct_id
            where 1=1 {where} order by a.face_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"bud_face.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_aoi_from_master(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select a.face_id, b.poi_bid, a.name_ch, 
            ST_Area(a.geom::geography) as area,
            st_perimeter(a.geom::geography) as perimeter,
            a.mesh_id, a.kind, a.aoi_level, st_astext(a.geom) as geom
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id 
            where 1=1 {where} order by a.face_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"blu_face.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_green_from_master(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select face_id,name_ch,name_ph,kind,admin_id,
            ST_Area(geom::geography) as area,
            st_perimeter(geom::geography) as perimeter,
            mesh_id,st_astext(geom) as geom
            from blc_face where kind in ('11', '12', '14', '15', '16', '21', '25')
            {where} order by face_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"blc_green.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_water_from_master(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select face_id,name_ch,name_ph,kind,admin_id,
            ST_Area(geom::geography) as area,
            st_perimeter(geom::geography) as perimeter,
            mesh_id,st_astext(geom) as geom
            from blc_face where kind in ('1', '2', '3', '4', '5', '6', '31', '32', '33') 
            {where} order by face_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"blc_water.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_brw_link_from_master(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select st_astext(geom) as geom,link_id,s_nid,e_nid,kind,form,length,name_ch,
            name_ph,name_en,z_level,mesh_id from brw_link where 1=1 
            {where} order by link_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"brw_link.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_brw_node_from_master(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select st_astext(geom) as geom,node_id,kind,form,mesh_id from brw_node where 1=1 
            {where} order by node_id
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"brw_node.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_vils_from_order(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select st_astext(geom) as geom,guid,village_code,village_name,town_code,town_name,
            county_code,county_name,city_code,city_name,pro_code,pro_name from bad_admin_vil_gcj
            where 1=1 {where} order by guid
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"bad_vils.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm -f {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def copy_towns_from_order(conf, export_id, where, where_params):
        """ 复制主表数据到csv """
        sql = f"""
            select st_astext(geom) as geom,guid,town_code,town_name,county_code,county_name,
            city_code,city_name,pro_code,pro_name from bad_admin_town_mzb where 1=1 {where} 
            order by guid
        """

        dest = os.path.join(helper.get_root_data_path(), str(export_id))
        file_path = os.path.join(dest, f"bad_towns.csv")
        if not os.path.exists(dest):
            os.makedirs(dest)
        else:
            os.system(f'rm {file_path}')

        OrderQuery.copy_table_to_file(conf, file_path, sql, where_params)

        return file_path

    @staticmethod
    def convert_coord_csv(in_path, coord, idx):
        """gcj02 csv 转坐标系 """

        out_path = in_path.replace('.csv', '_' + coord + '.csv')

        if os.path.exists(out_path):
            os.remove(out_path)

        gen = tsv.read_tsv(in_path)
        headers = next(gen)
        tsv.write_tsv(out_path, [headers], mode='a')

        insert = []
        for row in tqdm(gen, desc='坐标转化', mininterval=10):
            row[idx] = 'SRID=4326;' + helper.transform_geom(row[idx], coord).wkt
            insert.append(row)

            if len(insert) > 20000:
                tsv.write_tsv(out_path, insert, mode='a')
                insert = []

        if len(insert) > 0:
            tsv.write_tsv(out_path, insert, mode='a')
            insert = []

        return out_path

    @staticmethod
    def query_brw_link(sd_conn, where, where_params):
        """ 查询铁路数据 """
        link_sql = f"""
            select st_astext(geom) as geom,link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph
            from sd_brw_link where 1=1 {where} order by link_id;
        """
        return pgsql.fetch_all(sd_conn, link_sql, where_params)

    @staticmethod
    def query_intersects_mesh(back_conn, geom):
        """ 查询几何涉及到的图幅"""
        sql = f"""
            SELECT mesh_id FROM mesh_conf_wkt WHERE st_intersects(wkt,st_geomfromtext(%s,4326));
        """
        return pgsql.fetch_all(back_conn, sql, [geom])


class OrderWriter:
    """商单读写"""
    @staticmethod
    def insert_step_log(log_entry):
        """写入到步骤日志"""
        with helper.create_beeflow_connection() as conn, conn.cursor() as curs:
            log_sql = f"""
                INSERT INTO business_order_export_step_log (export_id, step_name, status_code, error_message, remark)
                VALUES (%s, %s, %s, %s, %s);
            """
            curs.execute(log_sql, [log_entry["export_id"],
                                   log_entry["step_name"],
                                   log_entry["status_code"],
                                   log_entry["error_message"],
                                   log_entry["remark"]])
            conn.commit()

    @staticmethod
    def update_export_order(export_id, status="", remark="", result=""):
        """更新商单导出结果"""
        with helper.create_beeflow_connection() as conn, conn.cursor() as curs:
            sql = """
                update common_export set status = %s, remark = %s, result = %s where id = %s;
            """
            curs.execute(sql, (status, remark, result, export_id))
            conn.commit()

    @staticmethod
    def update_export_order_condition(export_id, conditions=""):
        """更新商单条件"""
        with helper.create_beeflow_connection() as conn, conn.cursor() as curs:
            sql = """
                update common_export set conditions = %s where id = %s;
            """
            curs.execute(sql, (conditions, export_id))
            conn.commit()

    @staticmethod
    def create_bud_table(conn, table):
        """创建建筑物交付表"""
        sql = f"""
            create table {table}(
                face_id     character varying(128) not null PRIMARY KEY,
                struct_id   character varying(128)              ,
                name_ch     character varying(120)              ,
                aoi_id      character varying(128)              ,
                height      float            not null default 0 ,
                floors      float            not null default 0 ,
                sub_floors  float            not null default 0 ,
                area        double precision not null default 0 ,
                perimeter   double precision not null default 0 ,
                mesh_id     character varying(128)              ,
                src         character varying(64)               ,
                create_time timestamp without time zone default '1970-01-01 00:00:00'::timestamp without time zone,
                geom        geometry
            );
            CREATE INDEX idx_bud_mesh_key ON {table}(mesh_id);
            CREATE INDEX idx_bud_struct_key ON {table}(struct_id);
            CREATE INDEX idx_bud_area_key ON {table}(area);
            CREATE INDEX gdx_bud_geom_key ON {table} USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_aoi_table(conn, table):
        """ 创建aoi交付表 """
        sql = f"""
            create table {table}(
                face_id    character varying(128) not null PRIMARY KEY,
                poi_bid    character varying(128)              ,
                poi_id     character varying(128)              ,
                area       double precision not null default 0 ,
                perimeter  double precision not null default 0 ,
                name_ch    character varying(120)              ,
                mesh_id    character varying(128)              ,
                kind       character varying(64),
                std_tag    character varying(64),
                admin_code character varying(10),
                aoi_level  integer  not null default 0,
                geom       geometry
            );
            CREATE INDEX idx_blu_mesh_key ON {table}(mesh_id);
            CREATE INDEX idx_blu_area_key ON {table}(area);
            CREATE INDEX idx_blu_poid_key ON {table}(poi_id);
            CREATE INDEX gdx_blu_geom_key ON {table} USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_quanguo_aoi_table(conn, table):
        """ 创建 aoi 过程表，建筑物压盖判定用 """
        sql = f"""
            create table {table}(
                face_id    character varying(128) not null PRIMARY KEY,
                mesh_id    character varying(128)              ,
                kind       character varying(64),
                geom       geometry
            );
            CREATE INDEX gdx_qgblu_geom_key ON {table} USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_quanguo_water_table(conn, table):
        """ 创建 water 过程表，建筑物压盖判定用 """
        sql = f"""
            create table {table}(
                face_id   character varying(128) not null PRIMARY KEY,
                name_ch   character varying(120)              ,
                kind      character varying(6)                ,
                mesh_id   character varying(128)              ,
                geom      geometry
            );
            CREATE INDEX idx_qgwater_mesh_key ON {table}(mesh_id);
            CREATE INDEX gdx_qgwater_geom_key ON {table} USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_water_table(conn, table):
        """ 创建水系交付表 """
        sql = f"""
            create table {table}(
                face_id   character varying(128) not null PRIMARY KEY,
                name_ch   character varying(120)              ,
                name_ph   character varying(1000)             ,
                kind      character varying(6)                ,
                admin_id  character varying(6)                ,
                area      double precision not null default 0 ,
                perimeter double precision not null default 0 ,
                mesh_id   character varying(128)              ,
                geom      geometry
            );
            CREATE INDEX idx_water_mesh_key ON {table}(mesh_id);
            CREATE INDEX gdx_water_geom_key ON {table} USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_green_table(conn, table):
        """ 创建绿地交付表 """
        sql = f"""
            create table {table}(
                face_id   character varying(128) not null PRIMARY KEY,
                name_ch   character varying(120)              ,
                name_ph   character varying(1000)             ,
                kind      character varying(6)                ,
                admin_id  character varying(6)                ,
                area      double precision not null default 0 ,
                perimeter double precision not null default 0 ,
                mesh_id   character varying(128)              ,
                geom      geometry
            );
            CREATE INDEX idx_green_mesh_key ON {table}(mesh_id);
            CREATE INDEX gdx_green_geom_key ON {table} USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_bad_table(conn):
        """ 创建行政区划交付表 """
        sql = f"""
            create table sd_bad_pro(
                guid     character varying(64)          ,
                pro_code character varying(24) not null ,
                pro_name character varying(255)         ,
                geom     geometry
            );
            create table sd_bad_city(
                guid      character varying(64)          ,
                city_code character varying(24) not null ,
                city_name character varying(255)         ,
                pro_code  character varying(24)          ,
                pro_name  character varying(255)         ,
                geom      geometry
            );
            create table sd_bad_county(
                guid        character varying(64)          ,
                county_code character varying(24) not null ,
                county_name character varying(255)         ,
                city_code   character varying(24)          ,
                city_name   character varying(255)         ,
                pro_code    character varying(24)          ,
                pro_name    character varying(255)         ,
                geom        geometry
            );
            create table sd_bad_town(
                guid        character varying(64)          ,
                town_code   character varying(24) not null ,
                town_name   character varying(255)         ,
                county_code character varying(24)          ,
                county_name character varying(255)         ,
                city_code   character varying(24)          ,
                city_name   character varying(255)         ,
                pro_code    character varying(24)          ,
                pro_name    character varying(255)         ,
                geom        geometry
            );
            create table sd_bad_village(
                guid         character varying(64) PRIMARY KEY,
                village_code character varying(24) not null ,
                village_name character varying(255)         ,
                town_code    character varying(24)          ,
                town_name    character varying(255)         ,
                county_code  character varying(24)          ,
                county_name  character varying(255)         ,
                city_code    character varying(24)          ,
                city_name    character varying(255)         ,
                pro_code     character varying(24)          ,
                pro_name     character varying(255)         ,
                geom         geometry
            );
            CREATE INDEX idx_sdtown_tcode_key ON sd_bad_town(town_code);
            CREATE INDEX gdx_sdtown_geom_key ON sd_bad_town USING gist(geom);
            CREATE INDEX idx_sdvil_vcode_key ON sd_bad_village(village_code);
            CREATE INDEX idx_sdvil_tcode_key ON sd_bad_village(town_code);
            CREATE INDEX idx_sdvil_ccode_key ON sd_bad_village(county_code);
            CREATE INDEX gdx_sdvil_geom_key ON sd_bad_village USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def create_brw_table(conn):
        """ 创建铁路交付表 """
        sql = f"""
            create table sd_brw_link(
                link_id   character varying(128) not null PRIMARY KEY,
                s_nid     character varying(128) not null     ,
                e_nid     character varying(128) not null     ,
                kind      smallint not null default 1         ,
                form      smallint not null default 0         ,
                length    double precision not null default 0 ,
                name_ch   character varying(120)              ,
                name_ph   character varying(1000)             ,
                name_en   character varying(1000)             ,
                z_level   character varying(1000)             ,
                mesh_id   character varying(128)              ,
                geom      geometry
            );
            create table sd_brw_node(
                node_id   character varying(128) not null PRIMARY KEY,
                kind      smallint not null default 1         ,
                form      smallint not null default 0         ,
                mesh_id   character varying(128)              ,
                geom      geometry
            );
            CREATE INDEX idx_sdbrw_mesh_key ON sd_brw_link(mesh_id);
            CREATE INDEX gdx_sdbrw_geom_key ON sd_brw_link USING gist(geom);
        """
        try:
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def drop_table(conn):
        """移除表"""
        with conn.cursor() as curs:
            for item in enums.DeliveryTables:
                try:
                    curs.execute("DROP TABLE {};".format(item.value))
                    conn.commit()
                except Exception as e:
                    print(e)
                    conn.rollback()

    @staticmethod
    def fix_snap(conn, table_name, where='', where_params=None):
        """几何精度统一调整，目前统一设置为7位以满足QGIS质检要求"""
        with conn.cursor() as curs:
            try:
                sql = f"update {table_name} set geom = ST_SnapToGrid(geom,0.0000001) where 1=1 {where};"
                curs.execute(sql, where_params)
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def fix_valid(conn, table_name, where='', where_params=None):
        """几何修复，部分几何体调整精度后会失效，需要进行修复"""
        with conn.cursor() as curs:
            try:
                sql = f"update {table_name} set geom = ST_MakeValid(geom) where ST_IsValid(geom)='f' {where};"
                curs.execute(sql, where_params)
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def fix_repeated_points(conn, table_name, where='', where_params=None):
        """几何修复，去除重复点"""
        with conn.cursor() as curs:
            try:
                sql = f"update {table_name} set geom = ST_RemoveRepeatedPoints(geom) where 1=1 {where};"
                curs.execute(sql, where_params)
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def fix_simply(conn, table_name, where='', where_params=None, tolerance=1e-7):
        """抽稀"""
        with conn.cursor() as curs:
            try:
                sql = f"update {table_name} set geom = ST_Simplify(geom, {tolerance}) where 1=1 {where};"
                curs.execute(sql, where_params)
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def fix_delete(conn, table_name, uqkey, uqvals):
        """删除记录"""
        with conn.cursor() as curs:
            try:
                sql = f"""
                delete from {table_name} where {uqkey}=any(%s);
                """
                curs.execute(sql, [uqvals])
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def fix_geometry_collection(conn, table_name, where='', where_params=None):
        """修复 ST_MakeValid 之后形成的 GEOMETRYCOLLECTION"""

        with conn.cursor() as curs:
            try:
                sql = f"""
                update {table_name} set geom = ST_CollectionExtract(geom, 3)
                where ST_GeometryType(geom) not in ('ST_MultiPolygon', 'ST_Polygon') {where};
                """
                curs.execute(sql, where_params)
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def sync_town(conn):
        """ 同步乡镇表 """
        try:
            town_fields = enums.BadFields.town_full()
            town_fields_str = ",".join(town_fields)
            sql = f"""
                insert into sd_bad_town (guid,{town_fields_str},geom)
                select REPLACE(uuid_generate_v4()::TEXT, '-', '') as guid,{town_fields_str},st_union(geom) as geom 
                from sd_bad_village group by {town_fields_str};
            """
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def sync_county(conn):
        """ 同步区县表 """
        try:
            fields = enums.BadFields.coun_full()
            fields_str = ",".join(fields)
            sql = f"""
                insert into sd_bad_county (guid,{fields_str},geom)
                select REPLACE(uuid_generate_v4()::TEXT, '-', '') as guid,{fields_str},st_union(geom) as geom 
                from sd_bad_town group by {fields_str};
            """
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def sync_city(conn):
        """ 同步城市表 """
        try:
            fields = enums.BadFields. city_full()
            fields_str = ",".join(fields)
            sql = f"""
                insert into sd_bad_city (guid,{fields_str},geom)
                select REPLACE(uuid_generate_v4()::TEXT, '-', '') as guid,{fields_str},st_union(geom) as geom 
                from sd_bad_county group by {fields_str};
            """
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def sync_pro(conn):
        """ 同步城市表 """
        try:
            fields = enums.BadFields. pro_full()
            fields_str = ",".join(fields)
            sql = f"""
                insert into sd_bad_pro (guid,{fields_str},geom)
                select REPLACE(uuid_generate_v4()::TEXT, '-', '') as guid,{fields_str},st_union(geom) as geom 
                from sd_bad_city group by {fields_str};
            """
            pgsql.execute(conn, sql)
        except Exception as e:
            conn.rollback()
            raise e

    @staticmethod
    def fix_brw_link_ph(conn, datas):
        """ 更新路段拼音 """
        try:
            sql = f"""
                update sd_brw_link set name_ph=%s where link_id=%s;
            """
            pgsql.execute_many(conn, sql, datas)
        except Exception as e:
            conn.rollback()
            raise e
