""" PDD 一些通用处理逻辑 """

import shapely.wkt
from mapio.utils import bid2uid
from src.business_order_pdd.table import gen_segments
from src.business_order_pdd.spec import ProcessCtx, QualityErrNo
from src.business_order_pdd import helper as helper2
from src.business_order import helper
from src.business_order.query import OrderWriter
from src.tools import pgsql
from itertools import chain

BUD_MIN_AREA = 2
AOI_MIN_AREA = 100  # AOI 最小面积阈值，低于的需要删除
AOI_NORMAL_MAX = 10000000  # AOI 常规垂类最大面积
AOI_MAX_AREA = 5000000000  # AOI 景区等垂类最大面积
AOI_MIN_ANGLE = 20  # AOI 尖角阈值
AOI_BIG_TAG = ['交通设施;飞机场', '自然地物;其他', '休闲娱乐;度假村', '行政地标;商圈', '旅游景点;风景区']
WATER_MIN_AREA = 50
GREEN_MIN_AREA = 10


def _process_repeated_points_and_contact(ctx: ProcessCtx, start_id: str, end_id: str):
    """ 处理重复点具体逻辑 """

    wait_simply = []
    wait_delete = []

    with pgsql.get_connection(ctx.PGConf) as conn, conn.cursor() as curs:

        sql = f"""
            select {ctx.PkKey}, st_astext(geom) as geom, area from {ctx.Table} 
            where {ctx.PkKey}>%s and {ctx.PkKey}<=%s {ctx.Where} order by {ctx.PkKey};
        """
        final_query = curs.mogrify(sql, [start_id, end_id]).decode("utf-8")  # 安全拼接 SQL
        datas = pgsql.fetch_all(conn, final_query)
        for _data in datas:
            _wkt = shapely.wkt.loads(_data[1])
            if helper.Geoser.check_repeated_points(_wkt):
                wait_simply.append(_data[0])
                continue
            if helper.Geoser.check_contact(_wkt):
                wait_delete.append(_data[0])

        if len(wait_simply) > 0:
            OrderWriter.fix_simply(conn, ctx.Table, f'and {ctx.PkKey}=any(%s)', [wait_simply], 1e-6)
            simplied_datas = pgsql.fetch_all(
                conn,
                f"select {ctx.PkKey}, st_astext(geom) as geom from {ctx.Table} where {ctx.PkKey}=any(%s)",
                [wait_simply])

            for _data in simplied_datas:
                _wkt = shapely.wkt.loads(_data[1])
                if helper.Geoser.check_contact(_wkt):
                    wait_delete.append(_data[0])

        if len(wait_delete) > 0:
            OrderWriter.fix_delete(conn, ctx.Table, ctx.PkKey, wait_delete)

    return {"contacted": wait_delete}  # 需要删除的是自接触数据，供后续日志输出


def _stand_process_overlap(ctx: ProcessCtx, start_id: str, end_id: str):
    """ 标准化压盖处理具体逻辑 """
    overlaps = []
    is_check_overlap = ctx.Other.get("overlap", False)
    is_check_cover = ctx.Other.get("cover", False)
    is_check_equals = ctx.Other.get("equal", False)
    with pgsql.get_connection(ctx.PGConf) as conn, conn.cursor() as curs:
        sql = f"""
            select {ctx.PkKey}, st_astext(geom) as geom from {ctx.Table} where {ctx.PkKey}>%s 
            and {ctx.PkKey}<=%s order by {ctx.PkKey};
        """

        lap_sql = f"""
            select {ctx.PkKey}, st_astext(geom) as geom from {ctx.Table} 
            where st_intersects(geom, st_geomfromtext(%s,4326)) 
            and {ctx.PkKey}<>%s;
        """

        datas = pgsql.fetch_all(conn, sql, [start_id, end_id])
        for _data in datas:
            laps = pgsql.fetch_all(conn, lap_sql, [_data[1], _data[0]])
            if len(laps) == 0:
                continue

            _data_wkt = shapely.wkt.loads(_data[1])
            for lap in laps:
                lap_wkt = shapely.wkt.loads(lap[1])
                if is_check_cover and (_data_wkt.covers(lap_wkt) or lap_wkt.covers(_data_wkt)):
                    overlaps.append((_data[0], lap[0]))

                if is_check_overlap and lap_wkt.overlaps(_data_wkt):
                    overlaps.append((_data[0], lap[0]))

                if is_check_equals and _data_wkt.equals(lap_wkt):
                    overlaps.append((_data[0], lap[0]))

    return overlaps


def stand_process_repeated_and_contact(ctx: ProcessCtx):
    """ 标准化重复点与自接触处理，为了效率放在一起 """
    with pgsql.get_connection(ctx.PGConf) as conn:
        _segs = gen_segments(conn, ctx.Table, ctx.PkKey, ctx.Where, ctx.Size)
        segments = [{"ctx": ctx, "start_id": x[0], "end_id": x[1]} for x in _segs]

    results = helper.multi_run(segments, _process_repeated_points_and_contact, 32)
    contacts = [id_ for res in results for id_ in res.get("contacted", [])]
    return contacts


def stand_process_overlap(ctx: ProcessCtx):
    """ 标准化压盖处理 """
    with pgsql.get_connection(ctx.PGConf) as conn:
        _segs = gen_segments(conn, ctx.Table, ctx.PkKey, ctx.Where, ctx.Size)
        segments = [{"ctx": ctx, "start_id": x[0], "end_id": x[1]} for x in _segs]

    results = helper.multi_run(segments, _stand_process_overlap, 32)
    del_ids = list(chain.from_iterable(results))  # 最高效的方式
    overlap_sorted = list({tuple(sorted(x)) for x in del_ids})

    return overlap_sorted  # 因压盖删除逻辑有差异，删除逻辑需要各自实现


def stand_process_overlap_delete(ctx: ProcessCtx, overlaps: list):
    """ 标准化压盖删除逻辑 """
    del_ids = [x[0] for x in overlaps]
    with pgsql.get_connection(ctx.PGConf) as conn:
        OrderWriter.fix_delete(conn, ctx.Table, ctx.PkKey, del_ids)


def bud_process_overlap_delete(ctx: ProcessCtx, overlaps: list):
    """ 建筑物删除压盖逻辑 """
    wait_del = []
    wait_logs = []
    with pgsql.get_connection(ctx.PGConf) as conn:
        query_sql = f"select {ctx.PkKey},src,create_time from {ctx.Table} where {ctx.PkKey}=%s;"
        for overlap in overlaps:
            bud1 = pgsql.fetch_one(conn, query_sql, [overlap[0]])
            bud2 = pgsql.fetch_one(conn, query_sql, [overlap[1]])
            budinfo = "b1:{} {} {}, b2:{} {} {}.".format(
                bud1[0], bud1[1], bud1[2],
                bud2[0], bud2[1], bud2[2],
            )
            if bud1[1] == 'IR' and bud2[1] != 'IR':
                wait_del.append(bud1[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud1[0]))
                continue
            elif bud1[1] != 'IR' and bud2[1] == 'IR':
                wait_del.append(bud2[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud2[0]))
                continue
            elif bud1[2] < bud2[2]:
                wait_del.append(bud1[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud1[0]))
                continue
            else:
                wait_del.append(bud2[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud2[0]))
                continue
        OrderWriter.fix_delete(conn, ctx.Table, ctx.PkKey, wait_del)
    return wait_logs


##### ================================ PDD ================================= ####

def _pdd_process_bud_attr(ctx: ProcessCtx, start_id: str, end_id: str):
    """ PDD 处理建筑物属性 """
    resp_logs = []
    with pgsql.get_connection(ctx.PGConf) as conn, conn.cursor() as curs:
        sql = f"""
            select {ctx.PkKey},area,ST_GeometryType(geom) as geom_type,wall_material 
            from {ctx.Table} where {ctx.PkKey}>%s and {ctx.PkKey}<=%s order by {ctx.PkKey};
        """
        final_query = curs.mogrify(sql, [start_id, end_id]).decode("utf-8")  # 安全拼接 SQL
        datas = pgsql.fetch_all(conn, final_query)
        wait_delete = []
        wait_update = []
        for _data in datas:
            if _data[1] < BUD_MIN_AREA:
                wait_delete.append(_data[0])
                resp_logs.append(dict(errno=QualityErrNo.B0030, delid=_data[0]))
                continue
            if _data[2] != 'ST_Polygon':
                wait_delete.append(_data[0])
                resp_logs.append(dict(errno=QualityErrNo.B0038, delid=_data[0]))
                continue
            if not _data[3] or _data[3] == 0:
                wait_update.append(_data[0])
                continue

        if len(wait_delete) > 0:
            OrderWriter.fix_delete(conn, ctx.Table, ctx.PkKey, wait_delete)

        if len(wait_update) > 0:
            curs.execute(f'update {ctx.Table} set wall_material=1 where {ctx.PkKey}=any(%s);', [wait_update])
            conn.commit()

    return resp_logs


def pdd_process_bud_attr(ctx: ProcessCtx):
    """ PDD 处理建筑物属性 """
    with pgsql.get_connection(ctx.PGConf) as conn:
        _segs = gen_segments(conn, ctx.Table, ctx.PkKey, ctx.Where, ctx.Size)
        segments = [{"ctx": ctx, "start_id": x[0], "end_id": x[1]} for x in _segs]

    results = helper.multi_run(segments, _pdd_process_bud_attr, 32)


def _pdd_process_aoi_attr(ctx: ProcessCtx, start_id: str, end_id: str, poi_conf):
    """ PDD 处理AOI属性 """

    delete_logs = []
    with pgsql.get_connection(ctx.PGConf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, poi_bid, area from {ctx.Table} where face_id>%s
            and face_id<=%s order by face_id;
        """
        update_sql = f'update {ctx.Table} set name_ch=%s,std_tag=%s,admin_id=%s,' + \
            'poi_id=%s,city_name=%s,name_ph=%s where face_id=%s;'
        poi_sql = f'select bid, std_tag, name, admin_code, city from poi where bid=any(%s);'

        wait_delete = []
        wait_update = []
        aois = pgsql.fetch_all(conn, sql, [start_id, end_id])
        # 只获取有效的poi_bid（非空且非None）
        bids = [x[1] for x in aois if x[1] and x[1].strip() != '']

        # 如果没有有效的poi_bid，直接返回
        if not bids:
            return delete_logs

        with pgsql.get_connection(poi_conf) as poi_conn:
            pois = pgsql.fetch_all(poi_conn, poi_sql, [bids])
            pois_set = {x[0]: x for x in pois}

        for aoi in aois:
            # 跳过poi_bid为空的记录，不进行处理（既不更新也不删除）
            if not aoi[1] or aoi[1].strip() == '':
                continue

            # 如果poi_bid不为空但在POI表中找不到对应记录，则删除
            if aoi[1] not in pois_set:
                delete_logs.append(dict(errno=QualityErrNo.A0029, delid=aoi[0], poi_bid=aoi[1]))
                wait_delete.append(aoi[0])
                continue

            if aoi[2] < AOI_MIN_AREA:
                wait_delete.append(aoi[0])
                delete_logs.append(dict(errno=QualityErrNo.A0024, delid=aoi[0]))
                continue

            _poi = pois_set[aoi[1]]
            if "行政区划" in _poi[1] or "行政地标" in _poi[1]:
                delete_logs.append(dict(errno=QualityErrNo.A0051, delid=aoi[0]))
                wait_delete.append(aoi[0])
                continue

            if '交通设施;桥' == _poi[1]:
                delete_logs.append(dict(errno=QualityErrNo.A0052, delid=aoi[0]))
                wait_delete.append(aoi[0])
                continue

            wait_update.append((_poi[2], _poi[1], _poi[3], bid2uid(_poi[0]),
                               _poi[4], helper2.get_pinyin(_poi[2]), aoi[0]))

        # 执行批量更新和删除操作
        if wait_update:
            pgsql.execute_many(conn, update_sql, wait_update)
        if wait_delete:
            OrderWriter.fix_delete(conn, ctx.Table, 'face_id', wait_delete)
    return delete_logs


def pdd_process_aoi_attr(ctx: ProcessCtx, poiconf):
    """ PDD 处理建筑物属性 """
    with pgsql.get_connection(ctx.PGConf) as conn:
        _segs = gen_segments(conn, ctx.Table, ctx.PkKey, ctx.Where, ctx.Size)
        segments = [{"ctx": ctx, "start_id": x[0], "end_id": x[1], "poi_conf": poiconf} for x in _segs]

    results = helper.multi_run(segments, _pdd_process_aoi_attr, 32)
