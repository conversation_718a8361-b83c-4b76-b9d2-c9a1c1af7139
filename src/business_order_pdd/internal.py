""" PDD逻辑层 """

import os
from tqdm import tqdm
from src.business_order_pdd.schema import PddSchema
from src.business_order_pdd.table import copy_data_to_table, copy_table_to_file, gen_segments
from src.business_order_pdd import base
from src.business_order import helper
from src.business_order_pdd import helper as helper2
from src.business_order_pdd import process
from src.business_order_pdd.spec import ProcessCtx
from src.tools import pgsql


def update_geom_by_safe(sd_conf, table, safe_path):
    """ 更新加密几何 """
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        safes = helper2.get_data_from_txt(safe_path, delimiter='\t', encode='iso-8859-1')
        wait_update = []
        for safe in safes:
            wait_update.append((safe['geom'], safe['face_id']))
        try:
            curs.executemany(f'update {table} set geom=st_geomfromtext(%s,4326) where face_id=%s', wait_update)
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def _update_pinyin(sd_conf, table, pk_key, cn_key, ph_key, start_id, end_id):
    """ 更新拼音子 """
    with pgsql.get_connection(sd_conf) as conn:
        up_sql = f"update {table} set {cn_key}=%s,{ph_key}=%s where {pk_key}=%s"
        wait_update = []
        datas = pgsql.fetch_all(
            conn, f'select {pk_key},{cn_key},{ph_key} from {table} where {pk_key}>%s and {pk_key}<=%s',
            [start_id, end_id])
        for data in datas:
            if data[1] != '' and data[2] != '':
                continue
            if data[1] != '' and data[2] == '':
                name_ph = helper2.get_pinyin(data[1])
                wait_update.append((data[1], name_ph, data[0]))
                continue
            if data[1] == '' and data[2] != '':
                wait_update.append(('', '', data[0]))
                continue

        with conn.cursor() as curs:
            curs.executemany(up_sql, wait_update)
            conn.commit()


def update_pinyin(sd_conf, table, pk_key, cn_key, ph_key):
    """更新拼音"""
    with pgsql.get_connection(sd_conf) as conn:
        _segs = gen_segments(conn, table, pk_key, f'', 10000)
        segments = [{'sd_conf': sd_conf, 'table': table, 'pk_key': pk_key, 'cn_key': cn_key, 'ph_key': ph_key,
                    'start_id': x[0], 'end_id': x[1]} for x in _segs]

    helper.multi_run(segments, _update_pinyin, 32)


class BudLogic:
    """建筑物逻辑"""
    @staticmethod
    def create_bud(ctx: base.PddContext):
        """创建建筑物表"""
        with pgsql.get_connection(ctx.sd_conf) as conn:
            pgsql.execute(conn, PddSchema.Bud)

    @staticmethod
    def filter_data(ctx: base.PddContext):
        """ 建筑物筛选入商单库 """

        copy_to_sql = f"""
            select face_id,struct_id,poi_id,poi_bid,aoi_id,roof_style,wall_material,
            st_area(geom::geography) as area,
            st_perimeter(geom::geography) as perimeter,
            mesh_id,src,create_time,ST_AsEWKT(geom) as geom from bud_face 
            order by face_id
        """
        file_path = copy_table_to_file(ctx.master_conf, ctx.dest_temp_copy(),
                                       ctx.SD_BUD, copy_to_sql.replace("\n", ' '))

        copy_from_sql = f"copy {ctx.SD_BUD}(face_id,struct_id,poi_id,poi_bid," + \
            "aoi_id,roof_style,wall_material,area,perimeter,mesh_id,src,create_time,geom)"
        copy_data_to_table(ctx.sd_conf, file_path, copy_from_sql)

    @staticmethod
    def process_bud_repeated_points(ctx: base.PddContext):
        """ 处理建筑物重复点 """
        contacts = process.stand_process_repeated_and_contact(ProcessCtx(
            Table=ctx.SD_BUD, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000
        ))
        print(f"删除了 {len(contacts)} 条自接触")

    @staticmethod
    def process_bud_bud_overlap(ctx: base.PddContext):
        """ 处理建筑物与建筑物之间的压盖 """
        q_ctx = ProcessCtx(Table=ctx.SD_BUD, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000,
                           Other={"overlap": True, "cover": True})
        overlaps = process.stand_process_overlap(q_ctx)
        overlap_logs = process.bud_process_overlap_delete(q_ctx, overlaps)

        return overlap_logs

    @staticmethod
    def process_bud_attr(ctx: base.PddContext):
        """ 处理建筑物属性 """
        q_ctx = ProcessCtx(Table=ctx.SD_BUD, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000)
        process.pdd_process_bud_attr(q_ctx)

    @staticmethod
    def export_release(ctx: base.PddContext):
        """ 导出建筑物 """
        exporter = helper.JExporter(ctx.sd_conf, ctx.RELEASE_FMT, ctx.RELEASE_ENC)
        export_root = ctx.dest_release()
        for province in tqdm(ctx.DELIVER_PROVINCES):
            if ctx.appoint and province not in ctx.appoint:
                continue
            sql = f"""
select face_id,struct_id,poi_id,poi_bid,aoi_id,roof_style,wall_material,area,perimeter,bf.geom 
from {ctx.SD_BUD} bf inner join {ctx.SD_MESH} mc on bf.mesh_id = mc.mesh_id 
where province_en = '{province}'
            """
            dest = os.path.join(export_root, ctx.BUD_DIR, province)
            exporter.export(dest, 'bud_face', sql)

    @staticmethod
    def export_safe(ctx: base.PddContext):
        """ 导出建筑物 """
        exporter = helper.JExporter(ctx.sd_conf, ctx.RELEASE_FMT, ctx.SAFE_ENC)
        export_root = ctx.dest_process()
        for province in tqdm(ctx.DELIVER_PROVINCES):
            if ctx.appoint and province not in ctx.appoint:
                continue
            sql = f"""
select face_id,struct_id,poi_id,poi_bid,aoi_id,roof_style,wall_material,area,perimeter,bf.geom 
from {ctx.SD_BUD} bf inner join {ctx.SD_MESH} mc on bf.mesh_id = mc.mesh_id 
where province_en = '{province}'
            """
            dest = os.path.join(export_root, ctx.BUD_DIR, province)
            exporter.export(dest, 'bud_face_safe', sql)


class AoiLogic:
    """ 行政区逻辑 """
    @staticmethod
    def create_aoi(ctx: base.PddContext):
        """创建 aoi 表"""
        with pgsql.get_connection(ctx.sd_conf) as conn:
            pgsql.execute(conn, PddSchema.Aoi)

    @staticmethod
    def create_poi(ctx: base.PddContext):
        """创建 poi 表"""
        with pgsql.get_connection(ctx.sd_conf) as conn:
            pgsql.execute(conn, PddSchema.Poi)

    @staticmethod
    def create_mesh(ctx: base.PddContext):
        """创建 mesh 表"""
        with pgsql.get_connection(ctx.sd_conf) as conn:
            pgsql.execute(conn, PddSchema.Mesh)

    @staticmethod
    def filter_aoi(ctx: base.PddContext):
        """ AOI 筛选入商单库 """

        copy_to_sql = f"""
            select a.face_id as face_id,b.poi_bid as poi_bid,st_area(a.geom::geography) as area,
            st_perimeter(a.geom::geography) as perimeter,a.name_ph as name_ph,a.name_en as name_en,
            a.aoi_level as aoi_level,a.mesh_id as mesh_id,ST_AsEWKT(a.geom) as geom 
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where a.src != 'SD' and a.kind != '52' order by face_id
        """
        file_path = copy_table_to_file(ctx.master_conf, ctx.dest_temp_copy(),
                                       ctx.SD_AOI, copy_to_sql.replace("\n", ' '))

        copy_from_sql = f"copy {ctx.SD_AOI}(face_id,poi_bid,area,perimeter,name_ph,name_en,aoi_level,mesh_id,geom)"
        copy_data_to_table(ctx.sd_conf, file_path, copy_from_sql)

    @staticmethod
    def filter_mesh(ctx: base.PddContext):
        """ 筛选图幅数据入库 """

        copy_to_sql = f"""
            select mesh_id,province as province_ch,province_en,city as cityname,city_en as cityname_ph,
            st_asewkt(geom) as geom from mesh_conf
        """
        file_path = copy_table_to_file(ctx.order_conf, ctx.dest_temp_copy(),
                                       ctx.SD_MESH, copy_to_sql.replace("\n", ' '))

        copy_from_sql = f"copy {ctx.SD_MESH}(mesh_id,province_ch,province_en,cityname,cityname_ph,geom)"
        copy_data_to_table(ctx.sd_conf, file_path, copy_from_sql)

    @staticmethod
    def filter_poi(ctx: base.PddContext):
        """ 筛选 POI 数据入库 """
        copy_to_sql = f"select bid,mesh_id,name,address,kind,tag,std_tag,status,relation_bid,admin_code," + \
            "city,click_pv,st_asewkt(geometry) as geometry from poi"
        file_path = copy_table_to_file(ctx.poi_conf, ctx.dest_temp_copy(), ctx.SD_POI, copy_to_sql)

        copy_from_sql = f"copy {ctx.SD_POI}(bid,mesh_id,name,address,kind,tag,std_tag," + \
            "status,relation_bid,admin_code,city,click_pv,geometry) "
        copy_data_to_table(ctx.sd_conf, file_path, copy_from_sql)

    @staticmethod
    def process_aoi_repeated_points(ctx: base.PddContext):
        """ 处理 AOI 重复点 """
        contacts = process.stand_process_repeated_and_contact(ProcessCtx(
            Table=ctx.SD_AOI, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000
        ))
        print(f"删除了 {len(contacts)} 条自接触")

    @staticmethod
    def process_aoi_aoi_overlap(ctx: base.PddContext):
        """ 处理 AOI 与 AOI 之间的压盖 """
        q_ctx = ProcessCtx(Table=ctx.SD_AOI, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000,
                           Other={"equal": True})
        overlaps = process.stand_process_overlap(q_ctx)
        process.stand_process_overlap_delete(q_ctx, overlaps)

        return overlaps

    @staticmethod
    def process_aoi_attr(ctx: base.PddContext):
        """ 处理AOI属性 """
        q_ctx = ProcessCtx(Table=ctx.SD_AOI, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=10000)
        process.pdd_process_aoi_attr(q_ctx, ctx.poi_conf)

    @staticmethod
    def comple_gangao(ctx: base.PddContext):
        """ 港澳数据补全 """

        with pgsql.get_connection(ctx.order_conf) as conn:
            qry_sql = f"""
select face_id,poi_bid,poi_id,area,perimeter,kind as std_tag,admin_id,
name_ch,name_ph,name_en,aoi_level,city_name,geom,mesh_id 
from {ctx.SOURCE_AOI_GANGAO}
            """
            aois = pgsql.fetch_all(conn, qry_sql)

        with pgsql.get_connection(ctx.sd_conf) as conn, conn.cursor() as curs:
            insert_sql = f"""
insert INTO {ctx.SD_AOI} (face_id,poi_bid,poi_id,area,perimeter,std_tag,
admin_id,name_ch,name_ph,name_en,aoi_level,city_name,geom,mesh_id) 
values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) ON CONFLICT (face_id) DO NOTHING
            """
            try:
                curs.executemany(insert_sql, [tuple(x) for x in aois])
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def export_release(ctx: base.PddContext):
        """ 导出 AOI """
        exporter = helper.JExporter(ctx.sd_conf, ctx.RELEASE_FMT, ctx.RELEASE_ENC)
        export_root = ctx.dest_release()
        for province in tqdm(ctx.DELIVER_PROVINCES):
            if ctx.appoint and province not in ctx.appoint:
                continue
            sql = f"""
select face_id,std_tag as kind,area,perimeter,name_ch,name_ph,name_en,admin_id,aoi_level,city_name,
poi_id,poi_bid,bf.geom from {ctx.SD_AOI} bf inner join {ctx.SD_MESH} mc on bf.mesh_id = mc.mesh_id 
where province_en = '{province}'
            """
            dest = os.path.join(export_root, ctx.AOI_DIR, province)
            exporter.export(dest, 'blu_face', sql)

    @staticmethod
    def export_safe(ctx: base.PddContext):
        """ 导出 AOI 待加密包 """
        exporter = helper.JExporter(ctx.sd_conf, ctx.SAFE_FMT, ctx.SAFE_ENC)
        export_root = ctx.dest_process()
        for province in tqdm(ctx.DELIVER_PROVINCES):
            if ctx.appoint and province not in ctx.appoint:
                continue
            sql = f"""
select face_id,std_tag as kind,area,perimeter,name_ch,name_ph,name_en,admin_id,aoi_level,city_name,
poi_id,poi_bid,bf.geom from {ctx.SD_AOI} bf inner join {ctx.SD_MESH} mc on bf.mesh_id = mc.mesh_id 
where province_en = '{province}'
            """
            dest = os.path.join(export_root, ctx.AOI_DIR, province)
            exporter.export(dest, 'blu_face_safe', sql)


class WaterLogic:
    """水系逻辑"""

    @staticmethod
    def create_water(ctx: base.PddContext):
        """创建水系表"""
        with pgsql.get_connection(ctx.sd_conf) as conn:
            pgsql.execute(conn, PddSchema.Water)

    @staticmethod
    def filter_water(ctx: base.PddContext):
        """筛选水系数据入库"""
        copy_to_sql = f"""
            select face_id,name_ch,name_ph,kind,admin_id,ST_Area(geom::geography) as area,
            st_perimeter(geom::geography) as perimeter,mesh_id,st_astext(geom) as geom,form,dis_class
            from blc_face where kind in ('1', '2', '3', '4', '5', '6', '31', '32', '33')
        """
        file_path = copy_table_to_file(ctx.master_conf, ctx.dest_temp_copy(),
                                       ctx.SD_WATER, copy_to_sql.replace("\n", ' '))

        copy_from_sql = f"copy {ctx.SD_WATER}(face_id,name_ch,name_ph,kind," + \
            "admin_id,area,perimeter,mesh_id,geom,form,dis_class)"
        copy_data_to_table(ctx.sd_conf, file_path, copy_from_sql)

    @staticmethod
    def comple_sea(ctx: base.PddContext):
        """ 补全海域数据 """
        with pgsql.get_connection(ctx.order_conf) as conn, conn.cursor() as curs:
            qry_sql = f"""
select face_id,name_ch,name_ph,kind,admin_id,area,perimeter,form,dis_class,mesh_id,geom from {ctx.SOURCE_SEA}
            """
            seas = pgsql.fetch_all(conn, qry_sql)

        with pgsql.get_connection(ctx.sd_conf) as conn, conn.cursor() as curs:
            insert_sql = f"""
insert {ctx.SD_WATER} (face_id,name_ch,name_ph,kind,admin_id,area,perimeter,form,dis_class,mesh_id,geom) 
values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) ON CONFLICT (face_id) DO NOTHING
            """
            try:
                curs.executemany(insert_sql, [tuple(x) for x in seas])
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e

    @staticmethod
    def process_water_repeated_points(ctx: base.PddContext):
        """ 处理水系重复点 """
        contacts = process.stand_process_repeated_and_contact(ProcessCtx(
            Table=ctx.SD_WATER, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000
        ))
        print(f"删除了 {len(contacts)} 条自接触")

    @staticmethod
    def process_water_overlap(ctx: base.PddContext):
        """ 处理水系与水系之间的压盖 """
        q_ctx = ProcessCtx(Table=ctx.SD_WATER, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000,
                           Other={"cover": True})
        overlaps = process.stand_process_overlap(q_ctx)
        process.stand_process_overlap_delete(q_ctx, overlaps)

        return overlaps

    @staticmethod
    def export_release(ctx: base.PddContext):
        """ 导出水系 """
        sql = f"""
            select face_id,name_ch,name_ph,kind,form,dis_class,admin_id,area,perimeter,geom from {ctx.SD_WATER}
        """
        dest = os.path.join(ctx.dest_release(), ctx.BLC_DIR)
        exporter = helper.JExporter(ctx.sd_conf, ctx.RELEASE_FMT, ctx.RELEASE_ENC)
        exporter.export(dest, 'blc_face_shuixi', sql)

    @staticmethod
    def export_safe(ctx: base.PddContext):
        """ 导出水系待加密包 """
        sql = f"""
            select face_id,name_ch,name_ph,kind,form,dis_class,admin_id,area,perimeter,bw.geom as geom 
            from {ctx.SD_WATER} bw
            inner join {ctx.SD_MESH} mc on bw.mesh_id = mc.mesh_id where mc.province_en = any(%s)
        """

        with pgsql.get_connection(ctx.sd_conf) as conn, conn.cursor() as curs:
            final_query = curs.mogrify(sql, [ctx.appoint]).decode("utf-8")  # 安全拼接 SQL

        dest = os.path.join(ctx.dest_process(), ctx.BLC_DIR)
        exporter = helper.JExporter(ctx.sd_conf, ctx.SAFE_FMT, ctx.SAFE_ENC)
        exporter.export(dest, 'blc_face_shuixi_safe', final_query)


class GreenLogic:
    """ 绿地逻辑 """
    @staticmethod
    def create_green(ctx: base.PddContext):
        """创建绿地表"""
        with pgsql.get_connection(ctx.sd_conf) as conn:
            pgsql.execute(conn, PddSchema.Green)

    @staticmethod
    def filter_green(ctx: base.PddContext):
        """筛选绿地数据入库"""
        copy_to_sql = f"""
            select face_id,name_ch,name_ph,kind,admin_id,ST_Area(geom::geography) as area,
            st_perimeter(geom::geography) as perimeter,mesh_id,st_astext(geom) as geom,form,dis_class
            from blc_face where kind in ('11', '14', '15', '16', '25')
        """
        file_path = copy_table_to_file(ctx.master_conf, ctx.dest_temp_copy(),
                                       ctx.SD_GREEN, copy_to_sql.replace("\n", ' '))

        copy_from_sql = f"copy {ctx.SD_GREEN}(face_id,name_ch,name_ph,kind," + \
            "admin_id,area,perimeter,mesh_id,geom,form,dis_class)"
        copy_data_to_table(ctx.sd_conf, file_path, copy_from_sql)

    @staticmethod
    def process_green_repeated_points(ctx: base.PddContext):
        """ 处理绿地重复点 """
        contacts = process.stand_process_repeated_and_contact(ProcessCtx(
            Table=ctx.SD_GREEN, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000
        ))
        print(f"删除了 {len(contacts)} 条自接触")

    @staticmethod
    def process_green_overlap(ctx: base.PddContext):
        """ 处理绿地与绿地之间的压盖 """
        q_ctx = ProcessCtx(Table=ctx.SD_GREEN, PkKey='face_id', PGConf=ctx.sd_conf, Where='', Size=20000,
                           Other={"cover": True})
        overlaps = process.stand_process_overlap(q_ctx)
        process.stand_process_overlap_delete(q_ctx, overlaps)

        return overlaps

    @staticmethod
    def export_release(ctx: base.PddContext):
        """ 导出绿地 """
        exporter = helper.JExporter(ctx.sd_conf, ctx.RELEASE_FMT, ctx.RELEASE_ENC)
        export_root = ctx.dest_release()
        for province in tqdm(ctx.DELIVER_PROVINCES):
            if ctx.appoint and province not in ctx.appoint:
                continue
            sql = f"""
select face_id,name_ch,name_ph,kind,form,dis_class,admin_id,area,perimeter,bf.geom 
from {ctx.SD_GREEN} bf inner join {ctx.SD_MESH} mc on bf.mesh_id = mc.mesh_id 
where province_en = '{province}'
            """
            dest = os.path.join(export_root, ctx.BLC_DIR, province)
            exporter.export(dest, 'blc_face_lvdi', sql)

    @staticmethod
    def export_safe(ctx: base.PddContext):
        """ 导出绿地待加密包 """
        exporter = helper.JExporter(ctx.sd_conf, ctx.SAFE_FMT, ctx.SAFE_ENC)
        export_root = ctx.dest_process()
        for province in tqdm(ctx.DELIVER_PROVINCES):
            if ctx.appoint and province not in ctx.appoint:
                continue
            sql = f"""
select face_id,name_ch,name_ph,kind,form,dis_class,admin_id,area,perimeter,bf.geom 
from {ctx.SD_GREEN} bf inner join {ctx.SD_MESH} mc on bf.mesh_id = mc.mesh_id 
where province_en = '{province}'
            """
            dest = os.path.join(export_root, ctx.BLC_DIR, province)
            exporter.export(dest, 'blc_face_lvdi_safe', sql)
