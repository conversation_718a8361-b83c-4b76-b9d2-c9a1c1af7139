# PDD商单数据处理系统项目文档

## 项目概述

PDD商单数据处理系统是一个用于处理地理空间数据的Python项目，主要负责建筑物(BUD)、兴趣区域(AOI)、水系(Water)、绿地(Green)等地理要素数据的筛选、处理、质检和导出。系统采用模块化设计，支持多种数据格式的导入导出，并提供完整的数据质量检查功能。

## 系统架构

### 核心模块结构

```
src/business_order_pdd/
├── base.py          # 核心上下文和配置管理
├── helper.py        # 工具函数
├── internal.py      # 业务逻辑实现
├── process.py       # 数据处理逻辑
├── public.py        # 对外接口定义
├── schema.py        # 数据库表结构定义
├── spec.py          # 数据结构和枚举定义
├── statistic.py     # 统计功能
└── table.py         # 数据库操作工具
```

### 设计模式

- **上下文管理模式**: 使用 `PddContext` 管理整个处理流程的配置和状态
- **策略模式**: 通过开关控制不同的处理策略
- **工厂模式**: 通过 `PDDInterface` 抽象接口和 `PddActualService` 具体实现
- **分段处理模式**: 大数据量采用分段并行处理提高效率

## 主要功能模块

### 1. 数据类型支持

系统支持以下四种主要地理要素类型（铁路功能预留但未实现）：

#### 建筑物 (BUD)
- **表名**: `sd_pdd_bud_face`
- **主要字段**: face_id, struct_id, poi_id, poi_bid, aoi_id, roof_style, wall_material, area, perimeter, geom
- **处理功能**: 重复点处理、压盖检测、属性验证、几何修复

#### 兴趣区域 (AOI) 
- **表名**: `sd_pdd_blu_face`
- **主要字段**: face_id, poi_id, poi_bid, std_tag, area, name_ch, name_ph, aoi_level, geom
- **处理功能**: 重复点处理、压盖检测、属性补全、拼音生成

#### 水系 (Water)
- **表名**: `sd_pdd_blc_water`
- **主要字段**: face_id, name_ch, name_ph, kind, admin_id, area, form, dis_class, geom
- **处理功能**: 重复点处理、压盖检测、海域数据补全

#### 绿地 (Green)
- **表名**: `sd_pdd_blc_green`
- **主要字段**: face_id, name_ch, name_ph, kind, admin_id, area, form, dis_class, geom
- **处理功能**: 重复点处理、压盖检测、属性验证

#### 铁路 (BRW) - 预留功能
- **状态**: 当前PDD模块中铁路功能尚未实现
- **预留配置**: 
  - 导出目录: `background/03-railway`
  - 质量检查错误码: L类(铁路线)、N类(铁路节点)
- **表结构**: 在其他业务模块中定义
  - `sd_brw_link`: link_id, s_nid, e_nid, kind, form, length, name_ch, name_ph, name_en, z_level, mesh_id, geom
  - `sd_brw_node`: node_id, kind, form, mesh_id, geom
- **注**: 铁路处理逻辑在 `src/business_order` 模块中实现，PDD模块暂未集成

### 2. 核心处理流程

#### 数据处理管道
1. **创建表结构** (`create_table`)
2. **数据筛选入库** (`filter_data`)
3. **数据质量处理** (`process_data`)
4. **数据导出** (`export_data`)
5. **统计分析** (`statistic_data`)

#### 质量检查功能
- **几何质量检查**: 重复点检测、自接触检测、几何类型验证
- **属性质量检查**: 必填字段验证、数据类型检查、取值范围验证
- **空间关系检查**: 压盖检测、包含关系验证
- **面积阈值检查**: 最小面积限制、最大面积限制

### 3. 配置管理系统

#### 开关控制
系统通过多个开关类控制处理流程：
- `BudSwitch`: 建筑物处理开关
- `AoiSwitch`: AOI处理开关  
- `WaterSwitch`: 水系处理开关
- `GreenSwitch`: 绿地处理开关
- `StaticSwitch`: 统计功能开关

#### 上下文管理
`PddContext` 类提供：
- 数据库连接配置管理
- 文件路径管理
- 省份和区域配置
- 导出格式配置

## 技术特性

### 1. 高性能处理
- **分段并行处理**: 大数据量自动分段，支持多线程并行处理
- **内存优化**: 流式处理避免大数据集内存溢出
- **批量操作**: 数据库批量插入和更新提高效率

### 2. 数据安全
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **数据加密**: 支持敏感数据加密处理
- **事务管理**: 完整的数据库事务支持

### 3. 扩展性设计
- **插件化架构**: 通过接口抽象支持功能扩展
- **配置驱动**: 通过配置文件控制处理行为
- **模块化设计**: 各功能模块独立，便于维护和扩展

## 数据流转

### 输入数据源
- **主库数据**: 从master数据库获取基础地理数据
- **POI数据**: 从poi数据库获取兴趣点信息
- **订单数据**: 从order数据库获取业务订单信息

### 处理过程
1. **数据抽取**: 从源数据库按条件筛选数据
2. **数据清洗**: 去除重复点、修复几何错误
3. **质量检查**: 执行完整的数据质量验证
4. **属性补全**: 补充缺失的属性信息
5. **格式转换**: 转换为目标格式

### 输出结果
- **发布数据**: MIDMIF格式的正式发布数据
- **安全数据**: TAB格式的待加密数据包
- **统计报告**: CSV格式的数据统计报告
- **质检日志**: 详细的质量检查结果

## 质量标准

### 几何质量要求
- 建筑物最小面积: 2平方米
- AOI最小面积: 100平方米  
- 水系最小面积: 50平方米
- 绿地最小面积: 10平方米
- 最小夹角: 10-20度（根据要素类型）

### 属性质量要求
- 必填字段不能为空
- 字符串长度符合规范
- 数值类型和范围正确
- 外键关联完整性

## 部署和使用

### 环境依赖
- Python 3.x
- PostgreSQL数据库
- PostGIS空间扩展
- 相关Python包: psycopg2, shapely, tqdm等

### 配置要求
- 数据库连接配置 (order, master, poi)
- 文件路径配置
- 处理参数配置

### 使用示例
```python
from src.business_order_pdd.base import PddContext
from src.business_order_pdd.public import PddActualService

# 创建上下文
with PddContext(sd_conf, "project_name") as ctx:
    # 配置处理开关
    ctx.bud_switch.CreateBud = True
    ctx.bud_switch.FilterBud = True
    
    # 执行处理
    service = PddActualService()
    service.create_table(ctx)
    service.filter_data(ctx)
    service.process_data(ctx)
    service.export_data(ctx)
```

## 监控和日志

### 质量检查日志
系统提供详细的质量检查错误码和日志记录，包括：
- 错误类型分类 (A类-AOI, B类-建筑物, G类-绿地, W类-水系, L类-铁路线, N类-铁路节点, D类-行政区划)
- 具体错误描述
- 问题数据标识
- 处理建议

### 处理进度监控
- 使用tqdm显示处理进度
- 分段处理状态跟踪
- 异常处理和恢复机制

---

*本文档基于代码分析生成，如有疑问请参考具体代码实现或联系开发团队。*