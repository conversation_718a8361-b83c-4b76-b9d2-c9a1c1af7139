import os
import click
from src.business_order_pdd.base import AoiSwitch, BudSwitch, GreenSwitch, PddContext, StaticSwitch, WaterSwitch
from src.business_order_pdd.public import PddActualService
from conf.config import JConf


@click.group()  # 定义命令组
def cli():
    pass


def gen_svc():
    return PddActualService(), PddContext(JConf.pg.pddv6.to_dict(), 'pddv6')


@cli.command()
@click.option('--provinces', '-p', multiple=True, help='指定处理的省份，如：-p shanghai -p zhejiang')
def bud_deal(provinces):
    service, ctx = gen_svc()
    with ctx:
        if provinces:
            ctx.set_appoint(list(provinces))
        else:
            ctx.set_appoint(['shanghai', 'zhejiang'])  # 默认值
        ctx.set_safe_encrypt_path(ctx.SD_BUD, os.path.join(
            JConf.root_path, 'download', 'pddv6_safe', 'bud_face_safe_done.txt'))
        ctx.bud_switch = BudSwitch(
            CreateBud=False,  # 创建建筑物交付表
            FilterBud=False,  # 筛选建筑物数据入成果库
            ProcessRepatedPoints=False, # 重复点处理
            ProcessBudOverlap=False,    # 压盖处理
            ProcessBudAttr=False,       # 属性处理
            ProcessSafeEncrypt=False,   # 安全加密
            ExportRelease=True,         # 导出交付成果
            ExportSafe=False            # 导出安全加密
        )
        service.create_table(ctx)
        service.filter_data(ctx)
        service.process_data(ctx)
        service.export_data(ctx)


@cli.command()
@click.option('--provinces', '-p', multiple=True, help='指定处理的省份，如：-p shanghai -p zhejiang')
def aoi_deal(provinces):
    service, ctx = gen_svc()
    with ctx:
        if provinces:
            ctx.set_appoint(list(provinces))
        else:
            print("默认输出上海")
            ctx.set_appoint(['shanghai'])
        ctx.set_safe_encrypt_path(ctx.SD_AOI, os.path.join(
            JConf.root_path, 'download', 'pddv6_safe', 'blu_face_safe_done.txt'))
        ctx.aoi_switch = AoiSwitch(
            CreateAoi=False,               # 创建 aoi 表
            CreateMesh=False,              # 创建图幅表
            FilterAoi=False,               # 筛选 aoi 数据入成果库
            FilterMesh=False,              # 筛选 图幅数据入成果库
            ProcessRepeatedPoints=False,   # 处理重复点
            ProcessOverlap=False,          # 处理压盖
            ProcessAoiAttr=False,          # 处理 aoi 属性
            ProcessComplGangao=False,      # 港澳补充
            ProcessPh=False,               # 拼音处理
            ProcessSafeEncrypt=False,      # 安全加密
            ExportRelease=True,            # 导出交付包
            ExportSafe=False               # 导出安全加密包
        )
        service.create_table(ctx)
        service.filter_data(ctx)
        service.process_data(ctx)
        service.export_data(ctx)


@cli.command()
def water_deal():
    service, ctx = gen_svc()
    # ctx.set_appoint(['shanghai'])
    ctx.set_safe_encrypt_path(ctx.SD_WATER, os.path.join(
        JConf.root_path, 'download', 'pddv6_safe', 'blc_face_shuixi_safe_done.txt'))
    ctx.water_switch = WaterSwitch(
        CreateWater=False,          # 创建水系表
        FilterWater=False,          # 筛选水系数据入成果库
        ProcessRepeatedPoints=False,# 重复点处理
        ProcessOverlap=False,       # 压盖处理
        ProcessComplSea=False,      # 补充海域数据
        ProcessPh=False,            # 处理拼音
        ProcessSafeEncrypt=False,   # 处理安全加密
        ExportRelease=True,         # 导出交付包
        ExportSafe=False            # 导出安全加密包
    )
    service.create_table(ctx)
    service.filter_data(ctx)
    service.process_data(ctx)
    service.export_data(ctx)


@cli.command()
@click.option('--provinces', '-p', multiple=True, help='指定处理的省份，如：-p shanghai -p zhejiang')
def green_deal(provinces):
    service, ctx = gen_svc()
    with ctx:
        if provinces:
            ctx.set_appoint(list(provinces))
        else:
            print("默认输出上海")
            ctx.set_appoint(['shanghai'])
        ctx.set_safe_encrypt_path(ctx.SD_GREEN, os.path.join(
            JConf.root_path, 'download', 'pddv6_safe', 'blc_face_lvdi_safe_done.txt'))
        ctx.green_switch = GreenSwitch(
            CreateGreen=False,            # 创建绿地表
            FilterGreen=False,            # 筛选绿地数据入成果库
            ProcessRepeatedPoints=False,  # 处理重复点
            ProcessOverlap=False,         # 压盖处理
            ProcessPh=False,              # 拼音处理
            ProcessSafeEncrypt=False,     # 安全加密
            ExportRelease=True,           # 导出交付包
            ExportSafe=False              # 导出安全加密包
        )
        service.create_table(ctx)
        service.filter_data(ctx)
        service.process_data(ctx)
        service.export_data(ctx)


@cli.command
def statistic():
    service, ctx = gen_svc()
    ctx.stat_switch = StaticSwitch(
        DeliveryStatistic=True     # 导出交付成果交付量级
    )
    service.statistic_data(ctx)


if __name__ == '__main__':
    cli()
